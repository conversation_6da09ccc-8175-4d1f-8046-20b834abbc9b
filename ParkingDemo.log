2025-08-01 08:52:59.405  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 31176 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 08:52:59.407  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 08:52:59.433  INFO 31176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 08:52:59.433  INFO 31176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 08:53:00.384  INFO 31176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port(s): 8543 (http)
2025-08-01 08:53:00.389  INFO 31176 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 08:53:00.390  INFO 31176 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 08:53:00.440  INFO 31176 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 08:53:00.440  INFO 31176 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1006 ms
2025-08-01 08:53:00.811  WARN 31176 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 08:53:01.848  INFO 31176 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 08:53:01.873  INFO 31176 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 08:53:01.911  INFO 31176 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 08:53:02.072  INFO 31176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 08:53:02.073  INFO 31176 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 08:53:02.083  INFO 31176 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 08:53:02.116  INFO 31176 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 08:53:02.220  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 08:53:02.222  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 08:53:02.230  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 08:53:02.232  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 08:53:02.236  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 08:53:02.239  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 08:53:02.250  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 08:53:02.251  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 08:53:02.252  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 08:53:02.253  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 08:53:02.254  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 08:53:02.255  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 08:53:02.256  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 08:53:02.256  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 08:53:02.257  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 08:53:02.258  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 08:53:02.260  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 08:53:02.261  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 08:53:02.262  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 08:53:02.263  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 08:53:02.265  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 08:53:02.267  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 08:53:02.267  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 08:53:02.272  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 08:53:02.272  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 08:53:02.274  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 08:53:02.276  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 08:53:02.278  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 08:53:02.278  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 08:53:02.280  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 08:53:02.282  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 08:53:02.283  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 08:53:02.283  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 08:53:02.285  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 08:53:02.286  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 08:53:02.288  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 08:53:02.292  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 08:53:02.292  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 08:53:02.295  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 08:53:02.297  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 08:53:02.300  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 08:53:02.301  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 08:53:02.305  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 08:53:02.307  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 08:53:02.308  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 08:53:02.308  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 08:53:02.309  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 08:53:02.310  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 08:53:02.310  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 08:53:02.312  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 08:53:02.312  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 08:53:02.320  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 08:53:02.321  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 08:53:02.321  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 08:53:02.323  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 08:53:02.323  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 08:53:02.326  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 08:53:02.327  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 08:53:02.332  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 08:53:02.333  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 08:53:02.334  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 08:53:02.335  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 08:53:02.335  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 08:53:02.336  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 08:53:02.338  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 08:53:02.339  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 08:53:02.339  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 08:53:02.340  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 08:53:02.342  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 08:53:02.343  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 08:53:02.344  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 08:53:02.345  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 08:53:02.348  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 08:53:02.350  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 08:53:02.350  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 08:53:02.351  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 08:53:02.351  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 08:53:02.352  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 08:53:02.354  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 08:53:02.355  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 08:53:02.356  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 08:53:02.356  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 08:53:02.357  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 08:53:02.357  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 08:53:02.358  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 08:53:02.358  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 08:53:02.359  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 08:53:02.360  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 08:53:02.361  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 08:53:02.361  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 08:53:02.362  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 08:53:02.362  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 08:53:02.365  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 08:53:02.366  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 08:53:02.367  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 08:53:02.368  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 08:53:02.369  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 08:53:02.370  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 08:53:02.372  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 08:53:02.373  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 08:53:02.374  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 08:53:02.375  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 08:53:02.376  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 08:53:02.380  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 08:53:02.381  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 08:53:02.381  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 08:53:02.383  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 08:53:02.384  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 08:53:02.385  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 08:53:02.388  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 08:53:02.391  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 08:53:02.406  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 08:53:02.419  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 08:53:02.420  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 08:53:02.421  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 08:53:02.421  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 08:53:02.422  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 08:53:02.429  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 08:53:02.431  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 08:53:02.432  INFO 31176 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 08:53:02.447  INFO 31176 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 3.259 seconds (JVM running for 3.78)
2025-08-01 08:53:02.528  INFO 31176 --- [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 08:53:02.528  INFO 31176 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 08:53:02.529  INFO 31176 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 08:53:02.890  INFO 31176 --- [RMI TCP Connection(4)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 08:54:32.696  WARN 31176 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 79213
2025-08-01 09:01:50.405  WARN 31176 --- [http-nio-8543-exec-10] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 437694
2025-08-01 09:04:17.426  WARN 31176 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 117829
2025-08-01 09:07:27.242  WARN 31176 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 135925
2025-08-01 09:08:00.515  INFO 31176 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 09:08:00.523  INFO 31176 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-08-01 09:49:42.628  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 12172 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 09:49:42.629  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 09:49:42.651  INFO 12172 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 09:49:42.651  INFO 12172 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 09:49:44.292  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-08-01 09:49:44.296  INFO 12172 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 09:49:44.296  INFO 12172 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 09:49:44.341  INFO 12172 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 09:49:44.341  INFO 12172 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1690 ms
2025-08-01 09:49:44.820  WARN 12172 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 09:49:45.828  INFO 12172 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 09:49:45.852  INFO 12172 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 09:49:45.888  INFO 12172 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 09:49:46.059  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 09:49:46.060  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 09:49:46.066  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 09:49:46.095  INFO 12172 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 09:49:46.197  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 09:49:46.200  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 09:49:46.208  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 09:49:46.209  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 09:49:46.213  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 09:49:46.216  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 09:49:46.227  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 09:49:46.228  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 09:49:46.229  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 09:49:46.229  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 09:49:46.231  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 09:49:46.232  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 09:49:46.232  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 09:49:46.233  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 09:49:46.234  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 09:49:46.235  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 09:49:46.236  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 09:49:46.236  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 09:49:46.238  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 09:49:46.238  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 09:49:46.240  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 09:49:46.242  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 09:49:46.243  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 09:49:46.247  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 09:49:46.247  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 09:49:46.249  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 09:49:46.251  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 09:49:46.253  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 09:49:46.253  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 09:49:46.254  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 09:49:46.255  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 09:49:46.256  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 09:49:46.257  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 09:49:46.258  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 09:49:46.260  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 09:49:46.261  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 09:49:46.264  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 09:49:46.264  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 09:49:46.266  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 09:49:46.267  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 09:49:46.270  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 09:49:46.270  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 09:49:46.271  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 09:49:46.271  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 09:49:46.275  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 09:49:46.277  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 09:49:46.278  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 09:49:46.278  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 09:49:46.279  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 09:49:46.280  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 09:49:46.281  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 09:49:46.282  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 09:49:46.282  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 09:49:46.290  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 09:49:46.291  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 09:49:46.291  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 09:49:46.292  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 09:49:46.293  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 09:49:46.296  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 09:49:46.297  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 09:49:46.303  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 09:49:46.303  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 09:49:46.304  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 09:49:46.305  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 09:49:46.306  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 09:49:46.307  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 09:49:46.308  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 09:49:46.309  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 09:49:46.309  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 09:49:46.310  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 09:49:46.311  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 09:49:46.312  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 09:49:46.313  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 09:49:46.314  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 09:49:46.317  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 09:49:46.320  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 09:49:46.320  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 09:49:46.321  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 09:49:46.322  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 09:49:46.323  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 09:49:46.324  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 09:49:46.325  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 09:49:46.326  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 09:49:46.327  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 09:49:46.327  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 09:49:46.328  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 09:49:46.329  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 09:49:46.330  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 09:49:46.331  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 09:49:46.331  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 09:49:46.332  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 09:49:46.332  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 09:49:46.334  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 09:49:46.336  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 09:49:46.336  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 09:49:46.338  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 09:49:46.339  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 09:49:46.339  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 09:49:46.341  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 09:49:46.342  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 09:49:46.342  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 09:49:46.343  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 09:49:46.344  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 09:49:46.344  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 09:49:46.345  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 09:49:46.348  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 09:49:46.349  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 09:49:46.349  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 09:49:46.351  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 09:49:46.352  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 09:49:46.353  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 09:49:46.357  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 09:49:46.360  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 09:49:46.375  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 09:49:46.388  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 09:49:46.389  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 09:49:46.389  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 09:49:46.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 09:49:46.391  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 09:49:46.396  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 09:49:46.397  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 09:49:46.398  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 09:49:46.411  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 3.97 seconds (JVM running for 4.504)
2025-08-01 09:49:46.670  INFO 12172 --- [RMI TCP Connection(1)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 09:49:47.075  INFO 12172 --- [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 09:49:47.075  INFO 12172 --- [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 09:49:47.076  INFO 12172 --- [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 10:54:53.945  INFO 12172 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 10:54:53.950  INFO 12172 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-08-01 10:54:59.779  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 12172 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 10:54:59.779  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 10:55:01.118  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-08-01 10:55:01.118  INFO 12172 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 10:55:01.119  INFO 12172 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 10:55:01.168  INFO 12172 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 10:55:01.168  INFO 12172 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1386 ms
2025-08-01 10:55:01.518  WARN 12172 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 10:55:04.468  INFO 12172 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 10:55:04.513  INFO 12172 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 10:55:04.554  INFO 12172 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 10:55:04.831  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 10:55:04.832  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 10:55:04.834  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 10:55:04.901  INFO 12172 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 10:55:05.094  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 10:55:05.106  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 10:55:05.142  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 10:55:05.144  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 10:55:05.162  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 10:55:05.170  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 10:55:05.245  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 10:55:05.247  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 10:55:05.250  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 10:55:05.251  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 10:55:05.255  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 10:55:05.258  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 10:55:05.260  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 10:55:05.262  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 10:55:05.264  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 10:55:05.268  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 10:55:05.272  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 10:55:05.276  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 10:55:05.281  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 10:55:05.288  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 10:55:05.302  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 10:55:05.310  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 10:55:05.312  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 10:55:05.325  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 10:55:05.326  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 10:55:05.332  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 10:55:05.339  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 10:55:05.344  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 10:55:05.346  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 10:55:05.349  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 10:55:05.354  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 10:55:05.357  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 10:55:05.358  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 10:55:05.364  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 10:55:05.369  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 10:55:05.374  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 10:55:05.387  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 10:55:05.390  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 10:55:05.396  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 10:55:05.401  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 10:55:05.410  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 10:55:05.411  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 10:55:05.413  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 10:55:05.414  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 10:55:05.431  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 10:55:05.438  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 10:55:05.444  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 10:55:05.446  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 10:55:05.447  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 10:55:05.449  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 10:55:05.450  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 10:55:05.451  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 10:55:05.454  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 10:55:05.459  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 10:55:05.460  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 10:55:05.486  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 10:55:05.488  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 10:55:05.491  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 10:55:05.495  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 10:55:05.496  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 10:55:05.507  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 10:55:05.509  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 10:55:05.530  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 10:55:05.532  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 10:55:05.535  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 10:55:05.536  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 10:55:05.538  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 10:55:05.539  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 10:55:05.542  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 10:55:05.547  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 10:55:05.551  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 10:55:05.554  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 10:55:05.555  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 10:55:05.561  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 10:55:05.562  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 10:55:05.566  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 10:55:05.572  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 10:55:05.576  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 10:55:05.586  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 10:55:05.597  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 10:55:05.599  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 10:55:05.602  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 10:55:05.604  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 10:55:05.609  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 10:55:05.614  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 10:55:05.617  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 10:55:05.622  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 10:55:05.623  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 10:55:05.625  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 10:55:05.627  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 10:55:05.628  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 10:55:05.630  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 10:55:05.631  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 10:55:05.633  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 10:55:05.634  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 10:55:05.635  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 10:55:05.636  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 10:55:05.640  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 10:55:05.641  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 10:55:05.643  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 10:55:05.645  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 10:55:05.647  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 10:55:05.658  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 10:55:05.665  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 10:55:05.666  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 10:55:05.671  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 10:55:05.674  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 10:55:05.676  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 10:55:05.685  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 10:55:05.688  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 10:55:05.689  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 10:55:05.693  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 10:55:05.696  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 10:55:05.697  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 10:55:05.700  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 10:55:05.712  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 10:55:05.715  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 10:55:05.716  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 10:55:05.724  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 10:55:05.729  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 10:55:05.735  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 10:55:05.752  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 10:55:05.768  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 10:55:05.835  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 10:55:05.895  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 10:55:05.899  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 10:55:05.900  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 10:55:05.904  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 10:55:05.905  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 10:55:05.907  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 10:55:05.908  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 10:55:05.930  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 10:55:05.937  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 10:55:05.942  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 10:55:05.963  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 6.232 seconds (JVM running for 3924.056)
2025-08-01 10:55:05.969  INFO 12172 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-01 11:09:11.094  INFO 12172 --- [http-nio-8543-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:09:11.094  INFO 12172 --- [http-nio-8543-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 11:09:11.096  INFO 12172 --- [http-nio-8543-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 11:09:11.119  INFO 12172 --- [http-nio-8543-exec-1] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} inited
2025-08-01 11:11:07.571  WARN 12172 --- [http-nio-8543-exec-7] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 107535
2025-08-01 11:17:23.922  WARN 12172 --- [http-nio-8543-exec-3] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 263772
2025-08-01 11:19:21.139  WARN 12172 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 117059
2025-08-01 11:23:28.005  INFO 12172 --- [Thread-27] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-08-01 11:23:28.005  INFO 12172 --- [Thread-27] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-08-01 11:23:34.284  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 12172 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 11:23:34.284  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 11:23:35.784  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-08-01 11:23:35.786  INFO 12172 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 11:23:35.786  INFO 12172 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 11:23:35.858  INFO 12172 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 11:23:35.858  INFO 12172 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1572 ms
2025-08-01 11:23:36.319  WARN 12172 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 11:23:39.838  INFO 12172 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 11:23:39.876  INFO 12172 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 11:23:39.923  INFO 12172 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 11:23:40.197  INFO 12172 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 11:23:40.198  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 11:23:40.200  INFO 12172 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 11:23:40.252  INFO 12172 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 11:23:40.416  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 11:23:40.423  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 11:23:40.448  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 11:23:40.449  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 11:23:40.464  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 11:23:40.470  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 11:23:40.504  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 11:23:40.506  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 11:23:40.509  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 11:23:40.510  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 11:23:40.514  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 11:23:40.518  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 11:23:40.520  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 11:23:40.522  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 11:23:40.524  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 11:23:40.528  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 11:23:40.532  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 11:23:40.535  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 11:23:40.539  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 11:23:40.541  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 11:23:40.546  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 11:23:40.554  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 11:23:40.556  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 11:23:40.570  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 11:23:40.571  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 11:23:40.578  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 11:23:40.585  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 11:23:40.590  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 11:23:40.592  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 11:23:40.596  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 11:23:40.601  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 11:23:40.605  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 11:23:40.607  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 11:23:40.614  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 11:23:40.619  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 11:23:40.623  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 11:23:40.638  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 11:23:40.642  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 11:23:40.647  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 11:23:40.652  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 11:23:40.661  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 11:23:40.663  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 11:23:40.664  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 11:23:40.666  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 11:23:40.683  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 11:23:40.691  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 11:23:40.693  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 11:23:40.695  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 11:23:40.695  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 11:23:40.698  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 11:23:40.699  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 11:23:40.700  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 11:23:40.704  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 11:23:40.711  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 11:23:40.713  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 11:23:40.743  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 11:23:40.744  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 11:23:40.747  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 11:23:40.753  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 11:23:40.755  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 11:23:40.765  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 11:23:40.769  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 11:23:40.788  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 11:23:40.790  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 11:23:40.795  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 11:23:40.796  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 11:23:40.798  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 11:23:40.798  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 11:23:40.800  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 11:23:40.805  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 11:23:40.809  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 11:23:40.812  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 11:23:40.814  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 11:23:40.819  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 11:23:40.821  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 11:23:40.824  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 11:23:40.830  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 11:23:40.833  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 11:23:40.841  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 11:23:40.853  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 11:23:40.857  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 11:23:40.859  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 11:23:40.860  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 11:23:40.863  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 11:23:40.870  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 11:23:40.873  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 11:23:40.878  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 11:23:40.883  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 11:23:40.884  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 11:23:40.886  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 11:23:40.887  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 11:23:40.889  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 11:23:40.891  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 11:23:40.894  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 11:23:40.895  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 11:23:40.896  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 11:23:40.897  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 11:23:40.900  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 11:23:40.901  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 11:23:40.902  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 11:23:40.904  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 11:23:40.905  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 11:23:40.915  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 11:23:40.920  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 11:23:40.923  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 11:23:40.929  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 11:23:40.932  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 11:23:40.933  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 11:23:40.944  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 11:23:40.947  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 11:23:40.948  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 11:23:40.953  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 11:23:40.956  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 11:23:40.958  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 11:23:40.960  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 11:23:40.974  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 11:23:40.976  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 11:23:40.978  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 11:23:40.989  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 11:23:40.994  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 11:23:40.997  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 11:23:41.014  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 11:23:41.030  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 11:23:41.102  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 11:23:41.171  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 11:23:41.175  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 11:23:41.177  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 11:23:41.181  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 11:23:41.181  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 11:23:41.183  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 11:23:41.184  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 11:23:41.206  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 11:23:41.213  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 11:23:41.218  INFO 12172 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 11:23:41.239  INFO 12172 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 7.011 seconds (JVM running for 5639.332)
2025-08-01 11:23:41.243  INFO 12172 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-01 11:28:46.715  INFO 12172 --- [http-nio-8543-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 11:28:46.717  INFO 12172 --- [http-nio-8543-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 11:28:46.719  INFO 12172 --- [http-nio-8543-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-01 11:28:46.760  INFO 12172 --- [http-nio-8543-exec-1] com.alibaba.druid.pool.DruidDataSource   : {dataSource-3} inited
2025-08-01 11:29:43.067  INFO 12172 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-3} closing ...
2025-08-01 11:29:43.070  INFO 12172 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-3} closed
2025-08-01 13:33:24.398  INFO 5232 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 5232 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-08-01 13:33:24.399  INFO 5232 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-08-01 13:33:24.421  INFO 5232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 13:33:24.421  INFO 5232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 13:33:25.249  INFO 5232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-08-01 13:33:25.253  INFO 5232 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 13:33:25.253  INFO 5232 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 13:33:25.296  INFO 5232 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 13:33:25.296  INFO 5232 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 875 ms
2025-08-01 13:33:25.609  WARN 5232 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-08-01 13:33:26.615  INFO 5232 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 13:33:26.639  INFO 5232 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-08-01 13:33:26.673  INFO 5232 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 13:33:26.826  INFO 5232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-08-01 13:33:26.827  INFO 5232 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-08-01 13:33:26.835  INFO 5232 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-08-01 13:33:26.869  INFO 5232 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-08-01 13:33:26.977  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-08-01 13:33:26.980  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-08-01 13:33:26.989  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-08-01 13:33:26.990  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-08-01 13:33:26.994  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-08-01 13:33:26.998  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-08-01 13:33:27.009  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-08-01 13:33:27.010  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-08-01 13:33:27.011  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-08-01 13:33:27.011  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-08-01 13:33:27.012  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-08-01 13:33:27.014  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-08-01 13:33:27.014  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-08-01 13:33:27.015  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-08-01 13:33:27.015  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-08-01 13:33:27.017  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-08-01 13:33:27.018  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-08-01 13:33:27.018  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-08-01 13:33:27.020  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-08-01 13:33:27.021  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-08-01 13:33:27.022  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-08-01 13:33:27.024  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-08-01 13:33:27.025  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-08-01 13:33:27.030  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-08-01 13:33:27.030  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-08-01 13:33:27.032  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-08-01 13:33:27.034  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-08-01 13:33:27.035  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-08-01 13:33:27.036  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-08-01 13:33:27.037  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-08-01 13:33:27.039  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-08-01 13:33:27.040  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-08-01 13:33:27.040  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-08-01 13:33:27.042  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-08-01 13:33:27.044  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-08-01 13:33:27.045  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-08-01 13:33:27.049  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-08-01 13:33:27.050  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-08-01 13:33:27.052  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-08-01 13:33:27.054  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-08-01 13:33:27.057  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-08-01 13:33:27.058  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-08-01 13:33:27.059  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-08-01 13:33:27.059  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-08-01 13:33:27.065  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-08-01 13:33:27.067  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-08-01 13:33:27.067  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-08-01 13:33:27.068  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-08-01 13:33:27.068  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-08-01 13:33:27.069  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-08-01 13:33:27.069  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-08-01 13:33:27.070  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-08-01 13:33:27.070  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-08-01 13:33:27.072  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-08-01 13:33:27.072  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-08-01 13:33:27.079  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-08-01 13:33:27.080  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-08-01 13:33:27.081  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-08-01 13:33:27.082  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-08-01 13:33:27.083  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-08-01 13:33:27.086  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-08-01 13:33:27.087  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-08-01 13:33:27.093  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-08-01 13:33:27.093  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-08-01 13:33:27.094  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-08-01 13:33:27.095  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-08-01 13:33:27.095  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-08-01 13:33:27.095  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-08-01 13:33:27.096  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-08-01 13:33:27.097  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-08-01 13:33:27.098  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-08-01 13:33:27.099  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-08-01 13:33:27.099  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-08-01 13:33:27.101  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-08-01 13:33:27.101  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-08-01 13:33:27.102  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-08-01 13:33:27.103  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-08-01 13:33:27.104  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-08-01 13:33:27.107  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-08-01 13:33:27.109  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-08-01 13:33:27.110  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-08-01 13:33:27.110  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-08-01 13:33:27.111  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-08-01 13:33:27.112  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-08-01 13:33:27.114  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-08-01 13:33:27.115  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-08-01 13:33:27.116  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-08-01 13:33:27.116  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-08-01 13:33:27.117  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-08-01 13:33:27.117  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-08-01 13:33:27.117  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-08-01 13:33:27.118  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-08-01 13:33:27.118  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-08-01 13:33:27.119  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-08-01 13:33:27.119  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-08-01 13:33:27.119  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-08-01 13:33:27.120  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-08-01 13:33:27.120  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-08-01 13:33:27.121  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-08-01 13:33:27.121  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-08-01 13:33:27.122  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-08-01 13:33:27.122  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-08-01 13:33:27.124  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-08-01 13:33:27.126  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-08-01 13:33:27.126  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-08-01 13:33:27.127  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-08-01 13:33:27.128  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-08-01 13:33:27.129  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-08-01 13:33:27.131  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-08-01 13:33:27.132  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-08-01 13:33:27.133  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-08-01 13:33:27.134  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-08-01 13:33:27.134  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-08-01 13:33:27.135  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-08-01 13:33:27.135  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-08-01 13:33:27.138  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-08-01 13:33:27.139  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-08-01 13:33:27.139  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-08-01 13:33:27.141  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-08-01 13:33:27.142  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-08-01 13:33:27.143  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-08-01 13:33:27.146  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-08-01 13:33:27.149  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-08-01 13:33:27.162  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-08-01 13:33:27.174  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-08-01 13:33:27.175  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-08-01 13:33:27.175  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-08-01 13:33:27.176  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-08-01 13:33:27.176  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-08-01 13:33:27.177  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-08-01 13:33:27.177  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-08-01 13:33:27.182  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-08-01 13:33:27.183  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-08-01 13:33:27.184  INFO 5232 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-08-01 13:33:27.196  INFO 5232 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 2.97 seconds (JVM running for 3.455)
2025-08-01 13:33:27.420  INFO 5232 --- [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 13:33:27.420  INFO 5232 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 13:33:27.421  INFO 5232 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01 13:33:27.783  INFO 5232 --- [RMI TCP Connection(4)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-08-01 13:33:53.648 ERROR 5232 --- [http-nio-8543-exec-1] c.p.c.exception.GlobalExceptionHandler   : 异常信息：

org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:783) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:688) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:287) ~[spring-core-5.3.10.jar:5.3.10]
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2177) ~[jackson-core-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.writeString(UTF8JsonGenerator.java:521) ~[jackson-core-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.StringSerializer.serialize(StringSerializer.java:41) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeFields(MapSerializer.java:808) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeWithoutTypeInfo(MapSerializer.java:764) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:720) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:35) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:119) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeFields(MapSerializer.java:808) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serializeWithoutTypeInfo(MapSerializer.java:764) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:720) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.MapSerializer.serialize(MapSerializer.java:35) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:728) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:480) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:319) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1514) ~[jackson-databind-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1006) ~[jackson-databind-2.12.5.jar:2.12.5]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:454) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.5.5.jar:2.5.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_202]
	Suppressed: org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
		at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:783) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:688) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
		at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:287) ~[spring-core-5.3.10.jar:5.3.10]
		at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2177) ~[jackson-core-2.12.5.jar:2.12.5]
		at com.fasterxml.jackson.core.json.UTF8JsonGenerator.close(UTF8JsonGenerator.java:1220) ~[jackson-core-2.12.5.jar:2.12.5]
		at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:458) ~[spring-web-5.3.10.jar:5.3.10]
		... 56 common frames omitted
	Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
		at sun.nio.ch.SocketDispatcher.write0(Native Method)
		at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51)
		at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
		at sun.nio.ch.IOUtil.write(IOUtil.java:65)
		at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471)
		at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
		at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1363)
		at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:766)
		at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:586)
		at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:530)
		at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547)
		at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110)
		at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194)
		at org.apache.coyote.Response.doWrite(Response.java:615)
		at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340)
		... 65 common frames omitted
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_202]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_202]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_202]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_202]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471) ~[na:1.8.0_202]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1363) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:766) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:586) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:530) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:112) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.Response.doWrite(Response.java:615) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	... 87 common frames omitted

2025-08-01 13:33:53.651  WARN 5232 --- [http-nio-8543-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Failure in @ExceptionHandler com.parkingmanage.common.exception.GlobalExceptionHandler#error(HttpServletRequest, Exception)

org.apache.catalina.connector.ClientAbortException: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:353) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.flushByteBuffer(OutputBuffer.java:783) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.append(OutputBuffer.java:688) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.writeBytes(OutputBuffer.java:388) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.write(OutputBuffer.java:366) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteOutputStream.write(CoyoteOutputStream.java:96) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:287) ~[spring-core-5.3.10.jar:5.3.10]
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2177) ~[jackson-core-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1190) ~[jackson-core-2.12.5.jar:2.12.5]
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1008) ~[jackson-databind-2.12.5.jar:2.12.5]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:454) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) ~[spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:141) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.5.5.jar:2.5.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_202]
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_202]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_202]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_202]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_202]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:471) ~[na:1.8.0_202]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1363) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:766) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.writeBlocking(SocketWrapperBase.java:586) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketWrapperBase.write(SocketWrapperBase.java:530) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.doWrite(Http11OutputBuffer.java:547) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.doWrite(ChunkedOutputFilter.java:110) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11OutputBuffer.doWrite(Http11OutputBuffer.java:194) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.Response.doWrite(Response.java:615) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.OutputBuffer.realWriteBytes(OutputBuffer.java:340) ~[tomcat-embed-core-9.0.53.jar:9.0.53]
	... 69 common frames omitted

2025-08-01 13:34:05.819  INFO 5232 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-08-01 13:34:05.822  INFO 5232 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
